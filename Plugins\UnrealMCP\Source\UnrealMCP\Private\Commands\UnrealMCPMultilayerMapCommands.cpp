// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPMultilayerMapCommands.h"

// ========================================================================
// Constantes
// ========================================================================

// Tipos de resposta
const FString FUnrealMCPMultilayerMapCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPMultilayerMapCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPMultilayerMapCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPMultilayerMapCommands::RESPONSE_INFO = TEXT("info");

// Nomes das camadas
const FString FUnrealMCPMultilayerMapCommands::LAYER_PLANICIE_RADIANTE = TEXT("planicie_radiante");
const FString FUnrealMCPMultilayerMapCommands::LAYER_FIRMAMENTO_ZEPHYR = TEXT("firmamento_zephyr");
const FString FUnrealMCPMultilayerMapCommands::LAYER_ABISMO_UMBRAL = TEXT("abismo_umbral");

// Ranges Z das camadas
const float FUnrealMCPMultilayerMapCommands::PLANICIE_Z_MIN = 0.0f;
const float FUnrealMCPMultilayerMapCommands::PLANICIE_Z_MAX = 2000.0f;
const float FUnrealMCPMultilayerMapCommands::FIRMAMENTO_Z_MIN = 2000.0f;
const float FUnrealMCPMultilayerMapCommands::FIRMAMENTO_Z_MAX = 4000.0f;
const float FUnrealMCPMultilayerMapCommands::ABISMO_Z_MIN = 4000.0f;
const float FUnrealMCPMultilayerMapCommands::ABISMO_Z_MAX = 6000.0f;

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPMultilayerMapCommands::FUnrealMCPMultilayerMapCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada inicializado"));
}

FUnrealMCPMultilayerMapCommands::~FUnrealMCPMultilayerMapCommands()
{
    // Limpar caches
    LayerConfigCache.Empty();
    LayerStates.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_planicie_radiante_layer"))
    {
        return HandleCreatePlanicieRadianteLayer(Params);
    }
    else if (CommandType == TEXT("create_firmamento_zephyr_layer"))
    {
        return HandleCreateFirmamentoZephyrLayer(Params);
    }
    else if (CommandType == TEXT("create_abismo_umbral_layer"))
    {
        return HandleCreateAbismoUmbralLayer(Params);
    }
    else if (CommandType == TEXT("setup_complete_multilayer_system"))
    {
        return HandleSetupCompleteMultilayerSystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::HandleCreatePlanicieRadianteLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando camada Planície Radiante"));
    
    // Extrair configurações da camada
    FString LayerName = LAYER_PLANICIE_RADIANTE;
    bool bEnableLightCrystals = true;
    bool bSolarTowerEnhancement = true;
    bool bRegenerationBuffs = true;
    int32 JungleCampsCount = 24;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_light_crystals"), bEnableLightCrystals);
    CommandData->TryGetBoolField(TEXT("solar_tower_enhancement"), bSolarTowerEnhancement);
    CommandData->TryGetBoolField(TEXT("regeneration_buffs"), bRegenerationBuffs);
    CommandData->TryGetNumberField(TEXT("jungle_camps_count"), JungleCampsCount);
    
    // Criar configuração da camada
    TSharedPtr<FJsonObject> LayerConfig = MakeShared<FJsonObject>();
    LayerConfig->SetStringField(TEXT("layer_name"), LayerName);
    LayerConfig->SetNumberField(TEXT("z_min"), PLANICIE_Z_MIN);
    LayerConfig->SetNumberField(TEXT("z_max"), PLANICIE_Z_MAX);
    LayerConfig->SetBoolField(TEXT("enable_light_crystals"), bEnableLightCrystals);
    LayerConfig->SetBoolField(TEXT("solar_tower_enhancement"), bSolarTowerEnhancement);
    LayerConfig->SetBoolField(TEXT("regeneration_buffs"), bRegenerationBuffs);
    LayerConfig->SetNumberField(TEXT("jungle_camps_count"), JungleCampsCount);
    
    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    if (bEnableLightCrystals)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("accelerated_minion_growth_near_crystals")));
    }
    if (bSolarTowerEnhancement)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("solar_enhanced_towers")));
    }
    if (bRegenerationBuffs)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("regeneration_sustain_buffs")));
    }
    LayerConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);
    
    // Configurar layout das lanes
    TSharedPtr<FJsonObject> LaneLayout = MakeShared<FJsonObject>();
    LaneLayout->SetBoolField(TEXT("top"), true);
    LaneLayout->SetBoolField(TEXT("mid"), true);
    LaneLayout->SetBoolField(TEXT("bot"), true);
    LayerConfig->SetObjectField(TEXT("lane_layout"), LaneLayout);
    
    // Salvar configuração no cache
    LayerConfigCache.Add(LayerName, LayerConfig);
    
    // Criar estado da camada
    TSharedPtr<FJsonObject> LayerState = MakeShared<FJsonObject>();
    LayerState->SetStringField(TEXT("status"), TEXT("created"));
    LayerState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LayerState->SetBoolField(TEXT("active"), true);
    LayerStates.Add(LayerName, LayerState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("layer_config"), LayerConfig);
    ResponseData->SetObjectField(TEXT("layer_state"), LayerState);
    ResponseData->SetStringField(TEXT("layer_type"), TEXT("Planície Radiante - Camada Inferior"));
    ResponseData->SetStringField(TEXT("description"), TEXT("Camada familiar com 3 lanes tradicionais e jungle terrestre"));
    
    UE_LOG(LogTemp, Log, TEXT("Camada Planície Radiante criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Camada Planície Radiante criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::HandleCreateFirmamentoZephyrLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando camada Firmamento Zephyr"));
    
    // Extrair configurações da camada
    FString LayerName = LAYER_FIRMAMENTO_ZEPHYR;
    int32 WindCurrentsCount = 2;
    bool bCentralZone = true;
    bool bMobilePlatforms = true;
    float WindSpeedBoost = 0.3f;
    int32 PlatformChangeInterval = 180; // 3 minutos
    bool bEnableVortices = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("wind_currents_count"), WindCurrentsCount);
    CommandData->TryGetBoolField(TEXT("central_zone"), bCentralZone);
    CommandData->TryGetBoolField(TEXT("mobile_platforms"), bMobilePlatforms);
    CommandData->TryGetNumberField(TEXT("wind_speed_boost"), WindSpeedBoost);
    CommandData->TryGetNumberField(TEXT("platform_change_interval"), PlatformChangeInterval);
    CommandData->TryGetBoolField(TEXT("enable_vortices"), bEnableVortices);
    
    // Criar configuração da camada
    TSharedPtr<FJsonObject> LayerConfig = MakeShared<FJsonObject>();
    LayerConfig->SetStringField(TEXT("layer_name"), LayerName);
    LayerConfig->SetNumberField(TEXT("z_min"), FIRMAMENTO_Z_MIN);
    LayerConfig->SetNumberField(TEXT("z_max"), FIRMAMENTO_Z_MAX);
    LayerConfig->SetNumberField(TEXT("wind_currents_count"), WindCurrentsCount);
    LayerConfig->SetBoolField(TEXT("central_zone"), bCentralZone);
    LayerConfig->SetBoolField(TEXT("mobile_platforms"), bMobilePlatforms);
    LayerConfig->SetNumberField(TEXT("wind_speed_boost"), WindSpeedBoost);
    LayerConfig->SetNumberField(TEXT("platform_change_interval"), PlatformChangeInterval);
    LayerConfig->SetBoolField(TEXT("enable_vortices"), bEnableVortices);
    
    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("dynamic_layout_wind_currents")));
    if (bMobilePlatforms)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("mobile_platforms_3min_cycle")));
    }
    SpecialMechanics.Add(MakeShared<FJsonValueString>(FString::Printf(TEXT("wind_acceleration_%d_percent"), FMath::RoundToInt(WindSpeedBoost * 100))));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("floating_energy_bridges")));
    if (bEnableVortices)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("rapid_movement_vortices")));
    }
    LayerConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);
    
    // Salvar configuração no cache
    LayerConfigCache.Add(LayerName, LayerConfig);
    
    // Criar estado da camada
    TSharedPtr<FJsonObject> LayerState = MakeShared<FJsonObject>();
    LayerState->SetStringField(TEXT("status"), TEXT("created"));
    LayerState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LayerState->SetBoolField(TEXT("active"), true);
    LayerStates.Add(LayerName, LayerState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("layer_config"), LayerConfig);
    ResponseData->SetObjectField(TEXT("layer_state"), LayerState);
    ResponseData->SetStringField(TEXT("layer_type"), TEXT("Firmamento Zephyr - Camada Média"));
    ResponseData->SetStringField(TEXT("description"), TEXT("Camada dinâmica com correntes de vento e plataformas móveis"));
    
    UE_LOG(LogTemp, Log, TEXT("Camada Firmamento Zephyr criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Camada Firmamento Zephyr criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::HandleCreateAbismoUmbralLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando camada Abismo Umbral"));
    
    // Extrair configurações da camada
    FString LayerName = LAYER_ABISMO_UMBRAL;
    bool bLabyrinthineLayout = true;
    bool bShadowZones = true;
    float VisibilityReduction = 0.5f;
    bool bShadowFog = true;
    bool bProximityTraps = true;
    bool bSecretChambers = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("labyrinthine_layout"), bLabyrinthineLayout);
    CommandData->TryGetBoolField(TEXT("shadow_zones"), bShadowZones);
    CommandData->TryGetNumberField(TEXT("visibility_reduction"), VisibilityReduction);
    CommandData->TryGetBoolField(TEXT("shadow_fog"), bShadowFog);
    CommandData->TryGetBoolField(TEXT("proximity_traps"), bProximityTraps);
    CommandData->TryGetBoolField(TEXT("secret_chambers"), bSecretChambers);
    
    // Criar configuração da camada
    TSharedPtr<FJsonObject> LayerConfig = MakeShared<FJsonObject>();
    LayerConfig->SetStringField(TEXT("layer_name"), LayerName);
    LayerConfig->SetNumberField(TEXT("z_min"), ABISMO_Z_MIN);
    LayerConfig->SetNumberField(TEXT("z_max"), ABISMO_Z_MAX);
    LayerConfig->SetBoolField(TEXT("labyrinthine_layout"), bLabyrinthineLayout);
    LayerConfig->SetBoolField(TEXT("shadow_zones"), bShadowZones);
    LayerConfig->SetNumberField(TEXT("visibility_reduction"), VisibilityReduction);
    LayerConfig->SetBoolField(TEXT("shadow_fog"), bShadowFog);
    LayerConfig->SetBoolField(TEXT("proximity_traps"), bProximityTraps);
    LayerConfig->SetBoolField(TEXT("secret_chambers"), bSecretChambers);
    
    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    if (bLabyrinthineLayout)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("labyrinthine_corridors_chambers")));
    }
    if (bShadowZones)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(FString::Printf(TEXT("shadow_zones_%d_percent_visibility"), FMath::RoundToInt(VisibilityReduction * 100))));
    }
    if (bShadowFog)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("concealing_shadow_fog")));
    }
    if (bProximityTraps)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("proximity_environmental_traps")));
    }
    if (bSecretChambers)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("secret_reward_chambers")));
    }
    LayerConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);
    
    // Salvar configuração no cache
    LayerConfigCache.Add(LayerName, LayerConfig);
    
    // Criar estado da camada
    TSharedPtr<FJsonObject> LayerState = MakeShared<FJsonObject>();
    LayerState->SetStringField(TEXT("status"), TEXT("created"));
    LayerState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LayerState->SetBoolField(TEXT("active"), true);
    LayerStates.Add(LayerName, LayerState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("layer_config"), LayerConfig);
    ResponseData->SetObjectField(TEXT("layer_state"), LayerState);
    ResponseData->SetStringField(TEXT("layer_type"), TEXT("Abismo Umbral - Camada Superior"));
    ResponseData->SetStringField(TEXT("description"), TEXT("Camada labiríntica com zonas de sombra e armadilhas"));
    
    UE_LOG(LogTemp, Log, TEXT("Camada Abismo Umbral criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Camada Abismo Umbral criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::HandleSetupCompleteMultilayerSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Configurando sistema completo de três camadas"));

    // Configurações padrão
    bool bEnableAllLayers = true;
    bool bAutoConnectLayers = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_all_layers"), bEnableAllLayers);
    CommandData->TryGetBoolField(TEXT("auto_connect_layers"), bAutoConnectLayers);

    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    TArray<TSharedPtr<FJsonValue>> CreatedLayers;

    if (bEnableAllLayers)
    {
        // Criar Planície Radiante
        TSharedPtr<FJsonObject> PlanicieConfig = MakeShared<FJsonObject>();
        if (CommandData->HasField(TEXT("planicie_config")))
        {
            PlanicieConfig = CommandData->GetObjectField(TEXT("planicie_config"));
        }
        TSharedPtr<FJsonObject> PlanicieResult = HandleCreatePlanicieRadianteLayer(PlanicieConfig);
        CreatedLayers.Add(MakeShared<FJsonValueObject>(PlanicieResult));

        // Criar Firmamento Zephyr
        TSharedPtr<FJsonObject> FirmamentoConfig = MakeShared<FJsonObject>();
        if (CommandData->HasField(TEXT("firmamento_config")))
        {
            FirmamentoConfig = CommandData->GetObjectField(TEXT("firmamento_config"));
        }
        TSharedPtr<FJsonObject> FirmamentoResult = HandleCreateFirmamentoZephyrLayer(FirmamentoConfig);
        CreatedLayers.Add(MakeShared<FJsonValueObject>(FirmamentoResult));

        // Criar Abismo Umbral
        TSharedPtr<FJsonObject> AbismoConfig = MakeShared<FJsonObject>();
        if (CommandData->HasField(TEXT("abismo_config")))
        {
            AbismoConfig = CommandData->GetObjectField(TEXT("abismo_config"));
        }
        TSharedPtr<FJsonObject> AbismoResult = HandleCreateAbismoUmbralLayer(AbismoConfig);
        CreatedLayers.Add(MakeShared<FJsonValueObject>(AbismoResult));
    }

    SystemConfig->SetArrayField(TEXT("created_layers"), CreatedLayers);
    SystemConfig->SetBoolField(TEXT("auto_connect_layers"), bAutoConnectLayers);
    SystemConfig->SetNumberField(TEXT("total_layers"), CreatedLayers.Num());
    SystemConfig->SetStringField(TEXT("system_status"), TEXT("fully_configured"));
    SystemConfig->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());

    // Configurar conectividade se solicitado
    if (bAutoConnectLayers && CreatedLayers.Num() >= 2)
    {
        TSharedPtr<FJsonObject> ConnectivityConfig = MakeShared<FJsonObject>();
        ConnectivityConfig->SetNumberField(TEXT("portals_per_layer"), 4);
        ConnectivityConfig->SetNumberField(TEXT("elevators_total"), 4);
        ConnectivityConfig->SetBoolField(TEXT("dimensional_bridges_enabled"), true);
        SystemConfig->SetObjectField(TEXT("connectivity"), ConnectivityConfig);

        UE_LOG(LogTemp, Log, TEXT("Conectividade entre camadas configurada automaticamente"));
    }

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_type"), TEXT("AURACRON Multilayer Map System"));
    ResponseData->SetStringField(TEXT("description"), TEXT("Sistema completo de três camadas interconectadas"));

    UE_LOG(LogTemp, Log, TEXT("Sistema completo de mapa multicamada configurado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema completo de mapa multicamada configurado com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Error, TEXT("MultilayerMap Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPMultilayerMapCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    return Response;
}

bool FUnrealMCPMultilayerMapCommands::ValidateLayerConfig(const TSharedPtr<FJsonObject>& LayerConfig, FString& ErrorMessage)
{
    if (!LayerConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de camada inválida");
        return false;
    }

    // Validações básicas podem ser adicionadas aqui
    return true;
}
